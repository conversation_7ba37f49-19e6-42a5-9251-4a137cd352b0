package cloud.demand.lab.modules.longterm.cos.service.impl;

import cloud.demand.lab.modules.longterm.cos.dto.CosHistoryDataDTO;
import cloud.demand.lab.modules.longterm.cos.web.req.QueryPredictResultReq;
import cloud.demand.lab.modules.longterm.cos.web.resp.QueryPredictResultResp;
import com.pugwoo.dbhelper.DBHelper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

class CosModelPredictServiceImplTest {

    @Mock
    private DBHelper planCosDBHelper;

    @InjectMocks
    private CosModelPredictServiceImpl cosModelPredictService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    void testQueryPredictResult_WithTaskIdZero() {
        // 准备测试数据
        CosHistoryDataDTO dto1 = new CosHistoryDataDTO();
        dto1.setDate("2024-01-01");
        dto1.setScope("内部");
        dto1.setValue(new BigDecimal("100.50"));

        CosHistoryDataDTO dto2 = new CosHistoryDataDTO();
        dto2.setDate("2024-01-01");
        dto2.setScope("外部");
        dto2.setValue(new BigDecimal("200.30"));

        CosHistoryDataDTO dto3 = new CosHistoryDataDTO();
        dto3.setDate("2024-01-02");
        dto3.setScope("内部");
        dto3.setValue(new BigDecimal("150.75"));

        List<CosHistoryDataDTO> mockData = Arrays.asList(dto1, dto2, dto3);

        // Mock数据库查询
        when(planCosDBHelper.getRaw(any(Class.class), anyString())).thenReturn(mockData);

        // 准备请求
        QueryPredictResultReq req = new QueryPredictResultReq();
        req.setCategoryId(1L);
        req.setTaskId(0L);

        // 执行测试
        QueryPredictResultResp resp = cosModelPredictService.queryPredictResult(req);

        // 验证结果
        assertNotNull(resp);
        assertNotNull(resp.getLines());
        assertEquals(3, resp.getLines().size()); // 内部、外部、全部三条线

        // 验证内部数据线
        QueryPredictResultResp.Line innerLine = resp.getLines().stream()
                .filter(line -> "内部".equals(line.getScope()))
                .findFirst()
                .orElse(null);
        assertNotNull(innerLine);
        assertEquals("HISTORY", innerLine.getType());
        assertEquals(2, innerLine.getPoints().size());

        // 验证外部数据线
        QueryPredictResultResp.Line outerLine = resp.getLines().stream()
                .filter(line -> "外部".equals(line.getScope()))
                .findFirst()
                .orElse(null);
        assertNotNull(outerLine);
        assertEquals("HISTORY", outerLine.getType());
        assertEquals(1, outerLine.getPoints().size());

        // 验证全部数据线
        QueryPredictResultResp.Line totalLine = resp.getLines().stream()
                .filter(line -> "全部".equals(line.getScope()))
                .findFirst()
                .orElse(null);
        assertNotNull(totalLine);
        assertEquals("HISTORY", totalLine.getType());
        assertEquals(2, totalLine.getPoints().size());

        // 验证全部数据线的第一个点（2024-01-01的内部+外部）
        QueryPredictResultResp.Point firstPoint = totalLine.getPoints().stream()
                .filter(point -> "2024-01-01".equals(point.getDate()))
                .findFirst()
                .orElse(null);
        assertNotNull(firstPoint);
        assertEquals(new BigDecimal("300.80"), firstPoint.getValue()); // 100.50 + 200.30
    }

    @Test
    void testQueryPredictResult_WithEmptyData() {
        // Mock空数据
        when(planCosDBHelper.getRaw(any(Class.class), anyString())).thenReturn(Arrays.asList());

        // 准备请求
        QueryPredictResultReq req = new QueryPredictResultReq();
        req.setCategoryId(1L);
        req.setTaskId(0L);

        // 执行测试
        QueryPredictResultResp resp = cosModelPredictService.queryPredictResult(req);

        // 验证结果
        assertNotNull(resp);
        assertNotNull(resp.getLines());
        assertEquals(0, resp.getLines().size());
    }
}
