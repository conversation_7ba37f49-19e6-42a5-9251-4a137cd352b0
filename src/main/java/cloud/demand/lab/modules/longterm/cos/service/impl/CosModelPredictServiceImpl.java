package cloud.demand.lab.modules.longterm.cos.service.impl;

import cloud.demand.lab.modules.longterm.cos.service.CosBigCustomerHistoryChangeService;
import cloud.demand.lab.modules.longterm.cos.service.CosModelPredictService;
import cloud.demand.lab.modules.longterm.cos.web.dto.BigCustomerHistoryChangeDTO;
import cloud.demand.lab.modules.longterm.cos.web.dto.PlanCosScaleDataDTO;
import cloud.demand.lab.modules.longterm.cos.web.req.QueryBigCustomerHistoryChangeReq;
import cloud.demand.lab.modules.longterm.cos.web.req.QueryPredictResultReq;
import cloud.demand.lab.modules.longterm.cos.web.resp.QueryPredictResultResp;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.io.IOUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class CosModelPredictServiceImpl implements CosModelPredictService {

    @Resource
    private DBHelper planCosDBHelper;
    @Resource
    private CosBigCustomerHistoryChangeService cosBigCustomerHistoryChangeService;

    @Override
    public QueryPredictResultResp queryPredictResult(QueryPredictResultReq req) {
        QueryPredictResultResp resp = new QueryPredictResultResp();
        resp.setLines(new ArrayList<>());

        // 1. 查询大客户历史执行数据，用于在全量数据中进行剔除
        List<BigCustomerHistoryChangeDTO> bigCustomerHistoryChange =
                cosBigCustomerHistoryChangeService.queryBigCustomerHistoryChange(
                new QueryBigCustomerHistoryChangeReq(req.getCategoryId(), req.getTaskId())).getDataList();

        // 2. 查询历史数据，底数上分内外部，在内存中合并成全部
        if (req.getTaskId() == 0L) {
            queryHistoryRealtime(resp, bigCustomerHistoryChange);
        } else {
            // TODO 直接查有保存下来的底表数据
        }

        // 3. 预测数据 TODO

        return resp;
    }

    @SneakyThrows
    private void queryHistoryRealtime(QueryPredictResultResp resp, List<BigCustomerHistoryChangeDTO> bigCustomerHistoryChange) {
        String sql = IOUtils.readClasspathResourceAsString("/sql/longterm_predict/cos/plan_txy_cos_scale.sql");
        List<PlanCosScaleDataDTO> historyData = planCosDBHelper.getRaw(PlanCosScaleDataDTO.class, sql);

        // 3. 按scope分组数据
        Map<String, List<PlanCosScaleDataDTO>> scopeGroupedData = ListUtils.toMapList(historyData,
                o -> o.getScope(), o -> o);

        // 4.1 构建内部数据线
        QueryPredictResultResp.Line innerLine = buildHistoryLine("内部", scopeGroupedData.get("内部"),
                ListUtils.filter(bigCustomerHistoryChange, o -> !o.getIsOutCustomer()));
        resp.getLines().add(innerLine);
        // 4.2 构建外部数据线
        QueryPredictResultResp.Line outerLine = buildHistoryLine("外部", scopeGroupedData.get("外部"));
        resp.getLines().add(outerLine);

        // 4.3 构建全部数据线（内部+外部）
        QueryPredictResultResp.Line totalLine = buildTotalHistoryLine(historyData);
        resp.getLines().add(totalLine);
    }

    /**
     * 构建历史数据线
     */
    private QueryPredictResultResp.Line buildHistoryLine(String scope, List<PlanCosScaleDataDTO> data,
                                                         List<BigCustomerHistoryChangeDTO> bigCustomerHistoryChange) {
        QueryPredictResultResp.Line line = new QueryPredictResultResp.Line();
        line.setScope(scope);
        line.setType("HISTORY");
        line.setIncreaseInfos(new ArrayList<>());
        line.setPoints(new ArrayList<>());
        if (data != null) {
            for (PlanCosScaleDataDTO dto : data) {
                line.getPoints().add(ListUtils.of(dto.getDate(), dto.getValue()));
            }
        }
        return line;
    }

    /**
     * 构建全部数据线（内部+外部合并）
     */
    private QueryPredictResultResp.Line buildTotalHistoryLine(List<PlanCosScaleDataDTO> allData) {
        // 按日期分组，然后合并内外部数据
        Map<LocalDate, BigDecimal> dateValueMap = new HashMap<>();

        for (PlanCosScaleDataDTO dto : allData) {
            LocalDate date = dto.getDate();
            BigDecimal value = dto.getValue();
            dateValueMap.merge(date, value, BigDecimal::add);
        }

        QueryPredictResultResp.Line line = new QueryPredictResultResp.Line();
        line.setScope("全部");
        line.setType("HISTORY");
        line.setIncreaseInfos(new ArrayList<>());
        line.setPoints(new ArrayList<>());
        for (Map.Entry<LocalDate, BigDecimal> entry : dateValueMap.entrySet()) {
            line.getPoints().add(ListUtils.of(entry.getKey(), entry.getValue()));
        }
        ListUtils.sortAscNullLast(line.getPoints(), o -> (LocalDate) o.get(0));

        return line;
    }

}
